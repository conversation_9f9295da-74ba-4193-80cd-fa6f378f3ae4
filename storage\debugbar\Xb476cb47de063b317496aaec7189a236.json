{"__meta": {"id": "Xb476cb47de063b317496aaec7189a236", "datetime": "2025-05-29 15:05:54", "utime": 1748531154.101621, "method": "GET", "uri": "/livewire/livewire.js?id=38dc8241", "ip": "127.0.0.1"}, "php": {"version": "8.3.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748531153.114088, "end": 1748531154.101648, "duration": 0.9875600337982178, "duration_str": "988ms", "measures": [{"label": "Booting", "start": 1748531153.114088, "relative_start": 0, "end": 1748531153.741906, "relative_end": 1748531153.741906, "duration": 0.6278178691864014, "duration_str": "628ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1748531153.741929, "relative_start": 0.6278409957885742, "end": 1748531154.101651, "relative_end": 2.86102294921875e-06, "duration": 0.3597218990325928, "duration_str": "360ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43723256, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/livewire/livewire.js", "status_code": "<pre class=sf-dump id=sf-dump-1851203444 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1851203444\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/javascript; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-1672602655 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">38dc8241</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1672602655\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-467605352 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-467605352\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1432528089 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"715 characters\">XSRF-TOKEN=eyJpdiI6IkFQTWUySFVCMWdaejcyd2hUMlZmbHc9PSIsInZhbHVlIjoiS2h1SUFRNi82cS8zQUpWKzVZT21rVU84bFJLeU92TWZuNndQaVMxQ0ZkN1oxK2k1YVRmYlJlTXhwblRHZVA4SGtjM1Z5M0JycWdBRExlbERYR1djRWFCSUkyRkgvN0FRWEdwMU8rbXUyTnZxM1dvZXdpNVJVZUhrQTZtTWNIU00iLCJtYWMiOiIwMjZmZWNmZDFjMTU3ZGQ3ZDg5YTdjMTg4ODZhNjU0ZWIxODQzNWJlMDdmODc0NTc2NGRkNzM0ODg4N2Y3OTcwIiwidGFnIjoiIn0%3D; kaido_kit_session=eyJpdiI6IkhMTHZPRGNEY0t5czB5TFYrRkJvc1E9PSIsInZhbHVlIjoidFBUUjFtbVFwRVUrSXN3K2d4NnlaTUNHdGhMUlVORHNONmhId2dPWExkTFFjeUYvZ1YzcTY2QjliTDcwbHluNmNSdWJLenFDNXhuUkRmOThSN3hWakFqYVNHTmJ3LzFoa21NS0hieUZxTXhNKzlQN0dNSUF6cDRYK0ZXMXJNeC8iLCJtYWMiOiJkMjBkZGZjMzZlYWFjYzU4ZmU5YmZhMmI2OWNlYzU3MmZkNTAxNGE2YmFhYmQ2MDU2ZmIxM2YyYmRhOWZmY2FmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,id;q=0.8,ms;q=0.7,th;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"25 characters\">http://omnibis.test/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">omnibis.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1432528089\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkFQTWUySFVCMWdaejcyd2hUMlZmbHc9PSIsInZhbHVlIjoiS2h1SUFRNi82cS8zQUpWKzVZT21rVU84bFJLeU92TWZuNndQaVMxQ0ZkN1oxK2k1YVRmYlJlTXhwblRHZVA4SGtjM1Z5M0JycWdBRExlbERYR1djRWFCSUkyRkgvN0FRWEdwMU8rbXUyTnZxM1dvZXdpNVJVZUhrQTZtTWNIU00iLCJtYWMiOiIwMjZmZWNmZDFjMTU3ZGQ3ZDg5YTdjMTg4ODZhNjU0ZWIxODQzNWJlMDdmODc0NTc2NGRkNzM0ODg4N2Y3OTcwIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kaido_kit_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkhMTHZPRGNEY0t5czB5TFYrRkJvc1E9PSIsInZhbHVlIjoidFBUUjFtbVFwRVUrSXN3K2d4NnlaTUNHdGhMUlVORHNONmhId2dPWExkTFFjeUYvZ1YzcTY2QjliTDcwbHluNmNSdWJLenFDNXhuUkRmOThSN3hWakFqYVNHTmJ3LzFoa21NS0hieUZxTXhNKzlQN0dNSUF6cDRYK0ZXMXJNeC8iLCJtYWMiOiJkMjBkZGZjMzZlYWFjYzU4ZmU5YmZhMmI2OWNlYzU3MmZkNTAxNGE2YmFhYmQ2MDU2ZmIxM2YyYmRhOWZmY2FmIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 29 May 2026 15:05:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Oct 2024 19:35:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 29 May 2025 15:05:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">340160</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-367322162 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-367322162\", {\"maxDepth\":0})</script>\n"}}