<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Customer extends Model
{
    use HasFactory;

    protected $fillable = [
        'phone_number',
        'name',
        'chat_id',
        'account_type',
        'metadata',
    ];

    protected $casts = [
        'metadata' => 'array',
    ];

    // Relationships
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    // Scopes
    public function scopeWhatsApp($query)
    {
        return $query->where('account_type', 'whatsapp');
    }

    public function scopeTelegram($query)
    {
        return $query->where('account_type', 'telegram');
    }

    // Accessors
    public function getAccountTypeDisplayAttribute(): string
    {
        return match($this->account_type) {
            'whatsapp' => 'WhatsApp',
            'telegram' => 'Telegram',
            default => ucfirst($this->account_type)
        };
    }

    public function getDisplayNameAttribute(): string
    {
        return $this->name ?: ($this->phone_number ?: $this->chat_id);
    }

    public function getContactInfoAttribute(): string
    {
        if ($this->account_type === 'whatsapp') {
            return $this->phone_number ?: 'No phone number';
        } else {
            return $this->chat_id ?: 'No chat ID';
        }
    }

    // Helper methods
    public function getTotalOrdersAttribute(): int
    {
        return $this->orders()->count();
    }

    public function getTotalSpentAttribute(): float
    {
        return $this->orders()->sum('price');
    }
}
