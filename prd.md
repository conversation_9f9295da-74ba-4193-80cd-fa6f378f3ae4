---

## Product Requirements Document (PRD)

**IMPLEMENTATION STATUS: IN PROGRESS**
- ✅ Phase 0: Basic Filament setup (already completed)
- 🔄 Phase 1: Setup Core dan Manajemen Akun (in progress)
- ⏳ Phase 2: Penyimpanan Pesan dan Fitur Chat
- ⏳ Phase 3: Tracking Pesanan dan Manajemen Pelanggan
- ⏳ Phase 4: Katalog Produk
- ⏳ Phase 5: Integrasi Midtrans QRIS
- ⏳ Phase 6: Integrasi Bot n8n
- ⏳ Phase 7: Pengujian dan Debugging

### 1. Pendahuluan
**Tujuan Produk**:
Membangun Omnichannel CRM berbasis web untuk mengelola komunikasi pelanggan, tracking pesanan, pembayaran via QRIS (Midtrans), data pelanggan, dan katalog produk dalam satu platform terintegrasi. Produk ini dirancang untuk membantu bisnis mengotomatisasi dan menyederhanakan layanan pelanggan serta manajemen penjualan.

**<PERSON><PERSON><PERSON> yang Di<PERSON>kan**:
- Bisnis (UMKM hingga perusahaan besar) kesulitan mengelola interaksi pelanggan di berbagai channel (WhatsApp, Telegram) secara terpusat.
- Kurangnya integrasi antara komunikasi pelanggan, tracking pesanan, pembayaran, dan manajemen stok produk.
- Proses manual dalam setup otomatisasi (misalnya, bot) dan pembayaran memakan waktu.
- Kebutuhan akan fleksibilitas dalam tracking pesanan dan katalog produk dengan data kustom.

**Target Pengguna**:
- UMKM, startup, dan perusahaan besar yang membutuhkan solusi layanan pelanggan dan manajemen penjualan.
- Pengguna dengan kebutuhan integrasi WhatsApp (unofficial), Telegram, dan pembayaran QRIS.

**Konteks Pengembangan**:
- Menggunakan **Augment** dari Vibe Coding dengan **Agent Mode** untuk mempercepat pengembangan, debugging, dan integrasi.
- Agent Mode memungkinkan pembagian tugas ke agent (AI atau developer) untuk fokus pada fitur spesifik per fase.

---

### 2. Fitur Utama

1. **Manajemen Akun**:

    - Menambahkan dan mengelola akun WhatsApp (via API Baileys di VPS) dan Telegram (via token bot) di dashboard Laravel Filament.
    - Pengguna memasukkan kredensial (API Baileys atau token bot) untuk menghubungkan akun.

2. **Penyimpanan Pesan**:

    - Menyimpan semua pesan masuk dan keluar dari WhatsApp dan Telegram untuk tracking dan analisis.
    - Data pesan mencakup pengirim, isi pesan, timestamp, dan channel.

3. **Fitur Chat**:

    - Antarmuka chat di dashboard Filament untuk melihat dan membalas pesan secara real-time.
    - Notifikasi untuk pesan baru.

4. **Mode Bot**:

    - Integrasi dengan **n8n** untuk otomatisasi respons (setup manual via webhook URL).
    - Bot menangani pertanyaan seperti status pesanan, katalog produk, atau pengiriman QR code.

5. **Tracking Pesanan**:

    - Tabel fleksibel dengan kolom kustom (misalnya, Nama Pelanggan, Tanggal) disimpan dalam format JSON.
    - Kolom wajib: **Price** (desimal, IDR untuk QRIS), **Status Order** (enum: "pending", "shipped", "delivered").
    - Relasi dengan pelanggan (via nomor telepon/chat ID) dan produk (via product ID).
    - Mendukung impor data dari CSV/Excel atau API eksternal.

6. **Integrasi Midtrans QRIS**:

    - Pembayaran via QRIS dengan pengaturan on/off per akun di halaman pengaturan terpisah.
    - Menyimpan kredensial Midtrans (Merchant ID, Client Key, Server Key).
    - Proses checkout QRIS menghasilkan QR code/deeplink, ditampilkan di dashboard atau dikirim via bot.
    - Webhook Midtrans memperbarui status pesanan dan mengurangi **quantity** produk (jika diatur).
    - Pengurangan **quantity** produk:
        - Opsi 1: Saat pesanan dibuat (status "pending").
        - Opsi 2: Setelah pembayaran QRIS sukses (status "settlement").
        - Pengaturan per akun via toggle di halaman pengaturan.

7. **Manajemen Pelanggan**:

    - Data: **nomor telepon** (wajib untuk WhatsApp, opsional untuk Telegram), **nama** (auto-generated dari pesan/profil, bisa diedit), **chat ID** (Telegram), **jenis akun** (enum: "whatsapp", "telegram").
    - Halaman terpisah di Filament (`CustomerResource`) dengan filter berdasarkan jenis akun (Semua, WhatsApp, Telegram).
    - Relasi dengan pesanan untuk melacak riwayat.

8. **Katalog Produk (“Product Masbro”)**:
    - Data: **kode produk**, **nama**, **harga**, **mata uang** (IDR untuk QRIS), **deskripsi**, **gambar** (opsional), **quantity** (stok, integer ≥ 0), **status** (on/off, boolean).
    - Halaman terpisah di Filament (`ProductResource`) dengan filter berdasarkan status (Semua, On, Off).
    - **Quantity** otomatis berkurang berdasarkan pengaturan (saat pesanan dibuat atau pembayaran sukses).
    - Relasi dengan pesanan dan pelanggan.
    - Bot n8n menampilkan katalog produk (status “on”, dengan quantity).

---

### 3. Persyaratan Fungsional

1. **Manajemen Akun**:

    - Koneksi WhatsApp via API Baileys (di-host di VPS) dan Telegram via token bot.
    - Form Filament untuk input kredensial akun (API key atau token).

2. **Penyimpanan Pesan**:

    - Tabel `messages`: Kolom `id`, `account_id`, `sender`, `message`, `timestamp`, `channel` (enum: "whatsapp", "telegram").
    - Mendukung pencarian pesan berdasarkan pengirim atau isi.

3. **Fitur Chat**:

    - Antarmuka chat di Filament dengan tampilan daftar pesan per akun.
    - Notifikasi real-time menggunakan Laravel Echo atau Pusher.
    - Form untuk mengirim balasan langsung.

4. **Mode Bot**:

    - Setup webhook n8n untuk menangani pesan masuk dan menghasilkan respons otomatis.
    - Contoh respons: “Status pesanan ORD001: Rp100.000, Pending” atau “Katalog: Kaos Polos - Rp100.000 (stok: 50)”.

5. **Tracking Pesanan**:

    - Tabel `orders`: Kolom `id`, `customer_id`, `product_id`, `price` (desimal), `status` (enum: "pending", "shipped", "delivered"), `quantity_ordered` (integer), `custom_fields` (JSON).
    - Contoh JSON untuk kolom kustom:
        ```json
        {
            "custom_fields": [
                {
                    "name": "customer_name",
                    "type": "string",
                    "value": "Budi Santoso"
                },
                { "name": "order_date", "type": "date", "value": "2025-05-29" }
            ]
        }
        ```
    - UI Filament untuk membuat/mengedit kolom kustom, dengan **price** dan **status order** tetap.

6. **Midtrans QRIS**:

    - Tabel `midtrans_settings`: Kolom `account_id`, `merchant_id`, `client_key`, `server_key`, `qris_enabled` (boolean), `decrease_quantity_on` (enum: "order_created", "payment_success").
    - Tabel `order_payments`: Kolom `order_id`, `transaction_id`, `payment_type` ("qris"), `status`, `amount`, `currency`.
    - Tombol “Checkout with QRIS” di `OrderResource`, hanya muncul jika QRIS aktif.
    - Panggil Midtrans API (`SnapBi::qris()->createPayment()`) untuk membuat QR code/deeplink.
    - Contoh payload Midtrans QRIS:
        ```json
        {
            "partnerReferenceNo": "order-123",
            "amount": { "value": "100000.00", "currency": "IDR" },
            "merchantId": "M001234",
            "additionalInfo": {
                "acquirer": "gopay",
                "customerId": 1,
                "productId": 1
            }
        }
        ```
    - Webhook Midtrans untuk memperbarui **status order** dan **quantity** produk (jika diatur).

7. **Manajemen Pelanggan**:

    - Tabel `customers`: Kolom `id`, `phone_number`, `name`, `chat_id`, `account_type` (enum: "whatsapp", "telegram").
    - Nama auto-generated dari pesan/profil (misalnya, nama kontak WhatsApp atau username Telegram).
    - Filter jenis akun di Filament (dropdown: Semua, WhatsApp, Telegram).
    - Relasi dengan `orders` via `customer_id`.

8. **Katalog Produk**:
    - Tabel `products`: Kolom `id`, `code`, `name`, `price` (desimal), `currency` (varchar, default "IDR"), `description`, `image` (varchar, opsional), `quantity` (integer ≥ 0), `status` (boolean).
    - Filter status di Filament (dropdown: Semua, On, Off).
    - Quantity berkurang:
        - Saat pesanan dibuat (status "pending") atau setelah pembayaran sukses (status "settlement").
        - Pengaturan di `midtrans_settings` (`decrease_quantity_on`).
    - Validasi: Cek **quantity** cukup sebelum membuat pesanan atau QRIS.

---

### 4. Persyaratan Non-Fungsional

-   **Performa**: Menangani hingga 1.000 pesan/hari, 10.000 pesanan, 1.000 pelanggan, 100 produk, dan 100 transaksi QRIS/hari tanpa penurunan performa.
-   **Keamanan**:
    -   Enkripsi data pesan, pesanan, pelanggan, produk, dan kredensial Midtrans.
    -   Autentikasi pengguna dengan role-based access (admin, operator).
    -   Validasi webhook Midtrans via `signature_key`.
-   **Kompatibilitas**: Dashboard mendukung browser modern (Chrome, Firefox, Safari).
-   **Skalabilitas**: Mendukung penambahan channel lain (Instagram, email) di masa depan.

---

### 5. Asumsi dan Batasan

-   **Asumsi**:
    -   Pengguna memiliki VPS untuk API Baileys dan familiar dengan n8n.
    -   Pengguna memiliki akun Midtrans dan memahami format CSV/Excel untuk impor.
    -   Augment dengan Agent Mode digunakan untuk pengembangan.
-   **Batasan**:
    -   WhatsApp unofficial, setup bot manual.
    -   QRIS hanya mendukung IDR.
    -   **Status order** terbatas pada "pending", "shipped", "delivered".
    -   **Quantity** produk tidak negatif.
    -   Nama pelanggan auto-generated mungkin tidak selalu akurat.

---

### 6. Alur Pengguna (User Flow)

-   Pengguna login ke dashboard Filament.
-   Menambah akun WhatsApp (via API Baileys) atau Telegram (via token bot).
-   Menambah akun Midtrans di halaman pengaturan, mengaktifkan QRIS, dan memilih kapan **quantity** berkurang (saat pesanan/pembayaran).
-   Menambah produk di `ProductResource` (kode, nama, harga, quantity, status: on/off), dengan filter status.
-   Mengelola pelanggan di `CustomerResource` (nama auto-generated, jenis akun), dengan filter WhatsApp/Telegram.
-   Membuat pesanan di `OrderResource`, memilih pelanggan dan produk, **quantity** berkurang (jika diatur).
-   Klik “Checkout with QRIS” untuk menghasilkan QR code/deeplink, dikirim via bot atau ditampilkan di dashboard.
-   Webhook Midtrans memperbarui **status order** dan **quantity** (jika diatur).
-   Bot n8n menampilkan katalog produk (status “on”) atau status pesanan.

---

### 7. Metrik Keberhasilan

-   Jumlah akun WhatsApp/Telegram dan Midtrans yang dikelola.
-   Jumlah pelanggan (filter berdasarkan jenis akun) dan produk (filter berdasarkan status).
-   Tingkat respons pelanggan (waktu rata-rata balasan < 5 menit).
-   Adopsi fitur: jumlah pesanan, transaksi QRIS sukses, produk aktif, pelanggan aktif.
-   Kepuasan pengguna (NPS > 8 dari survei).

---

### 8. Development Phases (Menggunakan Augment dengan Agent Mode)

-   **Phase 1: Setup Core dan Manajemen Akun**:

    -   Agent: Inisialisasi proyek Laravel Filament, setup database, model, dan autentikasi.
    -   Tugas Augment: Generate kode untuk model `Account`, migration, dan form Filament untuk input kredensial API Baileys dan token bot Telegram.
    -   Output: Sistem autentikasi dan manajemen akun berfungsi.

-   **Phase 2: Penyimpanan Pesan dan Fitur Chat**:

    -   Agent: Bangun database pesan dan antarmuka chat di Filament.
    -   Tugas Augment: Generate schema tabel `messages`, form chat, dan integrasi notifikasi real-time (Laravel Echo).
    -   Output: Sistem penyimpanan pesan dan chat real-time.

-   **Phase 3: Tracking Pesanan dan Manajemen Pelanggan**:

    -   Agent: Implementasi tabel pesanan dengan JSON, `CustomerResource` dengan filter jenis akun.
    -   Tugas Augment: Generate model `Order`, `Customer`, migration, form Filament untuk kolom kustom, dan filter dropdown (WhatsApp/Telegram).
    -   Output: Fitur tracking pesanan dan manajemen pelanggan dengan filter.

-   **Phase 4: Katalog Produk**:

    -   Agent: Bangun `ProductResource` dengan quantity, status, dan filter status.
    -   Tugas Augment: Generate model `Product`, migration, form tambah/edit, dan filter dropdown (Semua, On, Off).
    -   Output: Katalog produk dengan quantity dan status.

-   **Phase 5: Integrasi Midtrans QRIS**:

    -   Agent: Setup `MidtransSettingsResource`, integrasi API Midtrans, webhook, dan pengurangan quantity.
    -   Tugas Augment: Generate kode untuk panggilan API Midtrans, validasi webhook, dan logika pengurangan quantity (berdasarkan pengaturan).
    -   Output: Fitur pembayaran QRIS dengan pengurangan quantity.

-   **Phase 6: Integrasi Bot n8n**:

    -   Agent: Konfigurasi webhook n8n untuk menampilkan katalog produk, detail pesanan, dan QR code.
    -   Tugas Augment: Generate skrip webhook dan logika respons bot (misalnya, format katalog produk).
    -   Output: Bot n8n terintegrasi dengan sistem.

-   **Phase 7: Pengujian dan Debugging**:
    -   Agent: Uji semua fitur, debug dengan Augment.
    -   Tugas Augment: Analisis bug, saran perbaikan kode, dan optimasi performa.
    -   Output: Sistem stabil dan siap rilis.

---

### 9. Entity-Relationship Diagram (ERD)

Berikut deskripsi teks untuk ERD yang menggambarkan relasi antar entitas dalam sistem. Karena saya tidak dapat menghasilkan diagram grafis, saya akan memberikan struktur tabel dalam format SQL dan penjelasan relasi.

#### Tabel dan Relasi

1. **Tabel `users`** (Pengguna sistem):

    - Kolom: `id` (PK), `name`, `email`, `password`, `created_at`, `updated_at`.
    - Relasi: Tidak ada relasi langsung, digunakan untuk autentikasi.

2. **Tabel `accounts`** (Akun WhatsApp/Telegram):

    - Kolom: `id` (PK), `user_id` (FK ke `users`), `type` (enum: "whatsapp", "telegram"), `credentials` (JSON, menyimpan API Baileys atau token bot), `created_at`, `updated_at`.
    - Relasi:
        - `user_id` → `users(id)` (many-to-one).
        - Digunakan oleh `messages`, `midtrans_settings`, dan `orders`.

3. **Tabel `messages`** (Pesan masuk/keluar):

    - Kolom: `id` (PK), `account_id` (FK ke `accounts`), `sender` (varchar), `message` (text), `timestamp` (datetime), `channel` (enum: "whatsapp", "telegram"), `created_at`, `updated_at`.
    - Relasi:
        - `account_id` → `accounts(id)` (many-to-one).

4. **Tabel `customers`** (Data pelanggan):

    - Kolom: `id` (PK), `phone_number` (varchar, nullable), `name` (varchar), `chat_id` (varchar, nullable), `account_type` (enum: "whatsapp", "telegram"), `created_at`, `updated_at`.
    - Relasi:
        - Digunakan oleh `orders` via `customer_id`.

5. **Tabel `products`** (Katalog produk):

    - Kolom: `id` (PK), `code` (varchar, unik), `name` (varchar), `price` (decimal), `currency` (varchar, default "IDR"), `description` (text, nullable), `image` (varchar, nullable), `quantity` (integer ≥ 0), `status` (boolean, default true), `created_at`, `updated_at`.
    - Relasi:
        - Digunakan oleh `orders` via `product_id`.

6. **Tabel `orders`** (Pesanan):

    - Kolom: `id` (PK), `account_id` (FK ke `accounts`), `customer_id` (FK ke `customers`), `product_id` (FK ke `products`), `price` (decimal), `status` (enum: "pending", "shipped", "delivered"), `quantity_ordered` (integer), `custom_fields` (JSON), `created_at`, `updated_at`.
    - Relasi:
        - `account_id` → `accounts(id)` (many-to-one).
        - `customer_id` → `customers(id)` (many-to-one).
        - `product_id` → `products(id)` (many-to-one).
        - Digunakan oleh `order_payments`.

7. **Tabel `midtrans_settings`** (Pengaturan Midtrans per akun):

    - Kolom: `id` (PK), `account_id` (FK ke `accounts`), `merchant_id` (varchar), `client_key` (varchar), `server_key` (varchar), `qris_enabled` (boolean), `decrease_quantity_on` (enum: "order_created", "payment_success"), `created_at`, `updated_at`.
    - Relasi:
        - `account_id` → `accounts(id)` (many-to-one).

8. **Tabel `order_payments`** (Data pembayaran QRIS):
    - Kolom: `id` (PK), `order_id` (FK ke `orders`), `transaction_id` (varchar), `payment_type` (varchar, default "qris"), `status` (varchar), `amount` (decimal), `currency` (varchar, default "IDR"), `created_at`, `updated_at`.
    - Relasi:
        - `order_id` → `orders(id)` (many-to-one).

#### Struktur SQL (Contoh)

```sql
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE accounts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    type ENUM('whatsapp', 'telegram') NOT NULL,
    credentials JSON NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);

CREATE TABLE messages (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    account_id BIGINT UNSIGNED NOT NULL,
    sender VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    timestamp DATETIME NOT NULL,
    channel ENUM('whatsapp', 'telegram') NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(id)
);

CREATE TABLE customers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    phone_number VARCHAR(20) NULL,
    name VARCHAR(255) NOT NULL,
    chat_id VARCHAR(255) NULL,
    account_type ENUM('whatsapp', 'telegram') NOT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE products (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    code VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    price DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'IDR',
    description TEXT NULL,
    image VARCHAR(255) NULL,
    quantity INT NOT NULL CHECK (quantity >= 0),
    status BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);

CREATE TABLE orders (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    account_id BIGINT UNSIGNED NOT NULL,
    customer_id BIGINT UNSIGNED NOT NULL,
    product_id BIGINT UNSIGNED NOT NULL,
    price DECIMAL(15,2) NOT NULL,
    status ENUM('pending', 'shipped', 'delivered') NOT NULL,
    quantity_ordered INT NOT NULL CHECK (quantity_ordered > 0),
    custom_fields JSON,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (product_id) REFERENCES products(id)
);

CREATE TABLE midtrans_settings (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    account_id BIGINT UNSIGNED NOT NULL,
    merchant_id VARCHAR(50) NOT NULL,
    client_key VARCHAR(255) NOT NULL,
    server_key VARCHAR(255) NOT NULL,
    qris_enabled BOOLEAN DEFAULT FALSE,
    decrease_quantity_on ENUM('order_created', 'payment_success') DEFAULT 'payment_success',
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (account_id) REFERENCES accounts(id)
);

CREATE TABLE order_payments (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    order_id BIGINT UNSIGNED NOT NULL,
    transaction_id VARCHAR(255) NOT NULL,
    payment_type VARCHAR(50) DEFAULT 'qris',
    status VARCHAR(50) NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'IDR',
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES orders(id)
);
```

#### Penjelasan Relasi

-   **Users ↔ Accounts**: Satu pengguna dapat memiliki banyak akun (WhatsApp/Telegram).
-   **Accounts ↔ Messages**: Satu akun dapat memiliki banyak pesan.
-   **Accounts ↔ Orders**: Satu akun dapat memiliki banyak pesanan.
-   **Accounts ↔ Midtrans Settings**: Satu akun memiliki satu pengaturan Midtrans.
-   **Customers ↔ Orders**: Satu pelanggan dapat memiliki banyak pesanan.
-   **Products ↔ Orders**: Satu produk dapat terkait dengan banyak pesanan.
-   **Orders ↔ Order Payments**: Satu pesanan dapat memiliki satu pembayaran QRIS.

---

### 10. Mockup UI (Deskripsi Teks)

1. **CustomerResource**:

    - **Tabel**: Kolom (Nama, Nomor Telepon, Chat ID, Jenis Akun: WhatsApp/Telegram, Aksi: Edit/Hapus).
    - **Filter**: Dropdown di atas tabel (Semua, WhatsApp, Telegram).
    - **Form Edit**: Input (Nama, Nomor Telepon, Chat ID, Jenis Akun: dropdown WhatsApp/Telegram), tombol “Simpan”.

2. **ProductResource**:

    - **Tabel**: Kolom (Kode Produk, Nama, Harga, Mata Uang, Quantity, Status: toggle on/off, Aksi: Edit/Hapus).
    - **Filter**: Dropdown di atas tabel (Semua, On, Off).
    - **Form Tambah/Edit**: Input (Kode, Nama, Harga, Mata Uang: dropdown IDR, Deskripsi, Quantity, Status: toggle), tombol “Simpan”.

3. **MidtransSettingsResource**:

    - **Tabel**: Kolom (Akun: WhatsApp/Telegram, Merchant ID, QRIS Enabled: toggle, Decrease Quantity: dropdown order_created/payment_success, Aksi: Edit/Hapus).
    - **Form Tambah/Edit**: Input (Akun: dropdown, Merchant ID, Client Key, Server Key, QRIS Enabled: toggle, Decrease Quantity: dropdown), tombol “Simpan”.

4. **OrderResource**:
    - **Tabel**: Kolom (Order ID, Nama Pelanggan, Jenis Akun, Produk, Quantity Ordered, Price, Status Order: dropdown pending/shipped/delivered, Aksi: Checkout QRIS/Edit).
    - **Modal QRIS**: Menampilkan QR code/deeplink, tombol “Kirim ke Pelanggan” (via WhatsApp/Telegram).

---

### Catatan Tambahan

-   **Augment dan Agent Mode**: Setiap fase dirancang agar agent (AI atau developer) dapat menggunakan Augment untuk menghasilkan kode (misalnya, model Laravel, form Filament) dan debugging (misalnya, validasi API Midtrans). Augment diasumsikan mendukung pembuatan kode boilerplate, analisis bug, dan integrasi API.
-   **Pengurangan Quantity**: Default ke “setelah pembayaran sukses” untuk mencegah pengurangan stok pada pesanan yang dibatalkan, dengan opsi per akun di `MidtransSettingsResource`.
-   **Mata Uang**: QRIS hanya mendukung IDR, sehingga **price** di `orders` dan `order_payments` menggunakan IDR.
-   **Contoh Struktur Data**:
    -   Tabel `customers`:
        ```json
        [
            {
                "id": 1,
                "phone_number": "+************",
                "name": "Budi Santoso",
                "chat_id": null,
                "account_type": "whatsapp"
            },
            {
                "id": 2,
                "phone_number": null,
                "name": "Siti Aminah",
                "chat_id": "*********",
                "account_type": "telegram"
            }
        ]
        ```
    -   Tabel `products`:
        ```json
        [
            {
                "id": 1,
                "code": "PROD001",
                "name": "Kaos Polos",
                "price": 100000.0,
                "currency": "IDR",
                "description": "Kaos katun",
                "quantity": 50,
                "status": true
            },
            {
                "id": 2,
                "code": "PROD002",
                "name": "Sepatu Sneaker",
                "price": 500000.0,
                "currency": "IDR",
                "description": "Sneaker kulit",
                "quantity": 20,
                "status": false
            }
        ]
        ```

Jika Anda ingin saya membuat contoh kode Filament untuk salah satu resource (misalnya, `CustomerResource` atau `ProductResource`), menambahkan fitur seperti notifikasi stok rendah, atau merevisi bagian tertentu dari PRD/ERD, silakan beri tahu. Apakah Anda ingin fase pengembangan diatur ulang atau detail Augment lebih spesifik?
