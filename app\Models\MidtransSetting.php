<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MidtransSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'account_id',
        'server_key',
        'client_key',
        'merchant_id',
        'environment',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    protected $hidden = [
        'server_key',
        'client_key',
    ];

    // Relationships
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeSandbox($query)
    {
        return $query->where('environment', 'sandbox');
    }

    public function scopeProduction($query)
    {
        return $query->where('environment', 'production');
    }

    // Accessors
    public function getEnvironmentDisplayAttribute(): string
    {
        return match($this->environment) {
            'sandbox' => 'Sandbox',
            'production' => 'Production',
            default => ucfirst($this->environment)
        };
    }

    public function getStatusDisplayAttribute(): string
    {
        return $this->is_active ? 'Active' : 'Inactive';
    }

    public function getMaskedServerKeyAttribute(): string
    {
        if (!$this->server_key) {
            return 'Not set';
        }
        return substr($this->server_key, 0, 8) . '...' . substr($this->server_key, -4);
    }

    public function getMaskedClientKeyAttribute(): string
    {
        if (!$this->client_key) {
            return 'Not set';
        }
        return substr($this->client_key, 0, 8) . '...' . substr($this->client_key, -4);
    }

    // Helper methods
    public function getBaseUrl(): string
    {
        return $this->environment === 'production' 
            ? 'https://api.midtrans.com/v2'
            : 'https://api.sandbox.midtrans.com/v2';
    }

    public function getSnapUrl(): string
    {
        return $this->environment === 'production'
            ? 'https://app.midtrans.com/snap/snap.js'
            : 'https://app.stg.midtrans.com/snap/snap.js';
    }
}
