<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Account extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'credentials',
        'name',
        'status',
    ];

    protected $casts = [
        'credentials' => 'array',
        'status' => 'boolean',
    ];

    protected $hidden = [
        'credentials',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function messages(): HasMany
    {
        return $this->hasMany(Message::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function midtransSettings(): HasOne
    {
        return $this->hasOne(MidtransSetting::class);
    }

    // Scopes
    public function scopeWhatsApp($query)
    {
        return $query->where('type', 'whatsapp');
    }

    public function scopeTelegram($query)
    {
        return $query->where('type', 'telegram');
    }

    public function scopeActive($query)
    {
        return $query->where('status', true);
    }

    // Accessors
    public function getTypeDisplayAttribute(): string
    {
        return match($this->type) {
            'whatsapp' => 'WhatsApp',
            'telegram' => 'Telegram',
            default => ucfirst($this->type)
        };
    }

    public function getStatusDisplayAttribute(): string
    {
        return $this->status ? 'Active' : 'Inactive';
    }
}
