<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CustomerResource\Pages;
use App\Filament\Resources\CustomerResource\RelationManagers;
use App\Models\Customer;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\KeyValue;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Infolists\Infolist;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;

class CustomerResource extends Resource
{
    protected static ?string $model = Customer::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Customer Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('name')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('Customer name'),

                Select::make('account_type')
                    ->required()
                    ->options([
                        'whatsapp' => 'WhatsApp',
                        'telegram' => 'Telegram',
                    ])
                    ->reactive()
                    ->afterStateUpdated(function ($state, callable $set) {
                        if ($state === 'whatsapp') {
                            $set('chat_id', null);
                        } else {
                            $set('phone_number', null);
                        }
                    }),

                TextInput::make('phone_number')
                    ->label('Phone Number')
                    ->tel()
                    ->maxLength(20)
                    ->placeholder('+************')
                    ->visible(fn($get) => $get('account_type') === 'whatsapp')
                    ->required(fn($get) => $get('account_type') === 'whatsapp'),

                TextInput::make('chat_id')
                    ->label('Chat ID')
                    ->maxLength(255)
                    ->placeholder('Telegram chat ID')
                    ->visible(fn($get) => $get('account_type') === 'telegram')
                    ->required(fn($get) => $get('account_type') === 'telegram'),

                KeyValue::make('metadata')
                    ->label('Additional Information')
                    ->keyLabel('Field')
                    ->valueLabel('Value')
                    ->addActionLabel('Add field')
                    ->helperText('Store additional customer information'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('account_type')
                    ->label('Account Type')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'whatsapp' => 'success',
                        'telegram' => 'info',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'whatsapp' => 'WhatsApp',
                        'telegram' => 'Telegram',
                        default => ucfirst($state),
                    }),

                TextColumn::make('contact_info')
                    ->label('Contact')
                    ->searchable(['phone_number', 'chat_id'])
                    ->getStateUsing(function ($record) {
                        return $record->contact_info;
                    }),

                TextColumn::make('total_orders')
                    ->label('Total Orders')
                    ->getStateUsing(function ($record) {
                        return $record->total_orders;
                    })
                    ->sortable(),

                TextColumn::make('total_spent')
                    ->label('Total Spent')
                    ->getStateUsing(function ($record) {
                        return 'IDR ' . number_format($record->total_spent, 0, ',', '.');
                    })
                    ->sortable(),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('account_type')
                    ->label('Account Type')
                    ->options([
                        'whatsapp' => 'WhatsApp',
                        'telegram' => 'Telegram',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCustomers::route('/'),
            'create' => Pages\CreateCustomer::route('/create'),
            'edit' => Pages\EditCustomer::route('/{record}/edit'),
        ];
    }
}
