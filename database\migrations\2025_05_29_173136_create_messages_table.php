<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->foreignId('account_id')->constrained()->onDelete('cascade');
            $table->string('sender'); // Phone number, username, or chat ID
            $table->text('message');
            $table->timestamp('timestamp');
            $table->enum('channel', ['whatsapp', 'telegram']);
            $table->enum('direction', ['incoming', 'outgoing'])->default('incoming');
            $table->string('message_id')->nullable(); // External message ID from platform
            $table->json('metadata')->nullable(); // Additional data like attachments, etc.
            $table->timestamps();

            // Indexes
            $table->index(['account_id', 'timestamp']);
            $table->index(['channel', 'direction']);
            $table->index('sender');
            $table->index('message_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('messages');
    }
};
