<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ProductResource\Pages;
use App\Filament\Resources\ProductResource\RelationManagers;
use App\Models\Product;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\FileUpload;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;

class ProductResource extends Resource
{
    protected static ?string $model = Product::class;

    protected static ?string $navigationIcon = 'heroicon-o-cube';

    protected static ?string $navigationGroup = 'Product Management';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('code')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(50)
                    ->placeholder('PROD001')
                    ->helperText('Unique product code'),

                TextInput::make('name')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('Product name'),

                TextInput::make('price')
                    ->required()
                    ->numeric()
                    ->prefix('IDR')
                    ->placeholder('100000'),

                Select::make('currency')
                    ->required()
                    ->options([
                        'IDR' => 'IDR (Indonesian Rupiah)',
                        'USD' => 'USD (US Dollar)',
                        'EUR' => 'EUR (Euro)',
                    ])
                    ->default('IDR'),

                Textarea::make('description')
                    ->rows(3)
                    ->placeholder('Product description'),

                FileUpload::make('image')
                    ->image()
                    ->directory('products')
                    ->visibility('public')
                    ->helperText('Upload product image'),

                TextInput::make('quantity')
                    ->required()
                    ->numeric()
                    ->minValue(0)
                    ->default(0)
                    ->helperText('Available stock quantity'),

                Toggle::make('status')
                    ->label('Active')
                    ->default(true)
                    ->helperText('Enable or disable this product'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                ImageColumn::make('image')
                    ->circular()
                    ->defaultImageUrl(url('/images/placeholder.png')),

                TextColumn::make('code')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->limit(30),

                TextColumn::make('formatted_price')
                    ->label('Price')
                    ->sortable('price'),

                TextColumn::make('quantity')
                    ->sortable()
                    ->badge()
                    ->color(fn($record) => $record->stock_status_color),

                TextColumn::make('stock_status')
                    ->badge()
                    ->color(fn($record) => $record->stock_status_color),

                ToggleColumn::make('status')
                    ->label('Active'),

                TextColumn::make('total_sold')
                    ->label('Sold')
                    ->getStateUsing(function ($record) {
                        return $record->total_sold;
                    })
                    ->sortable(),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                TernaryFilter::make('status')
                    ->label('Status')
                    ->placeholder('All products')
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only'),

                SelectFilter::make('stock_status')
                    ->label('Stock Status')
                    ->options([
                        'in_stock' => 'In Stock',
                        'low_stock' => 'Low Stock',
                        'out_of_stock' => 'Out of Stock',
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when(
                            $data['value'] === 'in_stock',
                            fn(Builder $query): Builder => $query->where('quantity', '>', 10),
                        )->when(
                            $data['value'] === 'low_stock',
                            fn(Builder $query): Builder => $query->where('quantity', '<=', 10)->where('quantity', '>', 0),
                        )->when(
                            $data['value'] === 'out_of_stock',
                            fn(Builder $query): Builder => $query->where('quantity', '<=', 0),
                        );
                    }),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }
}
