<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'account_id',
        'customer_id',
        'product_id',
        'price',
        'status',
        'quantity_ordered',
        'custom_fields',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'quantity_ordered' => 'integer',
        'custom_fields' => 'array',
    ];

    // Relationships
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    public function orderPayments(): HasMany
    {
        return $this->hasMany(OrderPayment::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopeShipped($query)
    {
        return $query->where('status', 'shipped');
    }

    public function scopeDelivered($query)
    {
        return $query->where('status', 'delivered');
    }

    // Accessors
    public function getStatusDisplayAttribute(): string
    {
        return match($this->status) {
            'pending' => 'Pending',
            'shipped' => 'Shipped',
            'delivered' => 'Delivered',
            default => ucfirst($this->status)
        };
    }

    public function getTotalAmountAttribute(): float
    {
        return $this->price * $this->quantity_ordered;
    }

    public function getFormattedPriceAttribute(): string
    {
        return 'IDR ' . number_format($this->price, 0, ',', '.');
    }

    public function getFormattedTotalAmountAttribute(): string
    {
        return 'IDR ' . number_format($this->total_amount, 0, ',', '.');
    }

    // Helper methods
    public function getCustomField(string $key, $default = null)
    {
        return $this->custom_fields[$key] ?? $default;
    }

    public function setCustomField(string $key, $value): void
    {
        $customFields = $this->custom_fields ?? [];
        $customFields[$key] = $value;
        $this->custom_fields = $customFields;
    }

    public function hasPayment(): bool
    {
        return $this->orderPayments()->exists();
    }

    public function getLatestPayment(): ?OrderPayment
    {
        return $this->orderPayments()->latest()->first();
    }
}
