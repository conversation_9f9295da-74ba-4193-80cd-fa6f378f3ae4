{"__meta": {"id": "Xc9cd6994789901223e65f532b06f0db1", "datetime": "2025-05-29 22:40:15", "utime": 1748558415.545163, "method": "GET", "uri": "/livewire/livewire.js?id=38dc8241", "ip": "127.0.0.1"}, "php": {"version": "8.3.6", "interface": "cgi-fcgi"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1748558414.986613, "end": 1748558415.545188, "duration": 0.558574914932251, "duration_str": "559ms", "measures": [{"label": "Booting", "start": 1748558414.986613, "relative_start": 0, "end": 1748558415.258661, "relative_end": 1748558415.258661, "duration": 0.2720479965209961, "duration_str": "272ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}, {"label": "Application", "start": 1748558415.258674, "relative_start": 0.2720608711242676, "end": 1748558415.545191, "relative_end": 3.0994415283203125e-06, "duration": 0.2865171432495117, "duration_str": "287ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time"}]}, "memory": {"peak_usage": 43866952, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET livewire/livewire.js", "controller": "Livewire\\Mechanisms\\FrontendAssets\\FrontendAssets@returnJavaScriptAsFile", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fomnibis%2Fvendor%2Flivewire%2Flivewire%2Fsrc%2FMechanisms%2FFrontendAssets%2FFrontendAssets.php&line=78\" onclick=\"\">vendor/livewire/livewire/src/Mechanisms/FrontendAssets/FrontendAssets.php:78-85</a>"}, "queries": {"nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/livewire/livewire.js", "status_code": "<pre class=sf-dump id=sf-dump-1425104580 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1425104580\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/javascript; charset=utf-8", "request_query": "<pre class=sf-dump id=sf-dump-426239503 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"8 characters\">38dc8241</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-426239503\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1930353406 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1930353406\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1597019252 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1175 characters\">remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82=eyJpdiI6Ikx2aU14clptQW4zank2S1JqQ1FhV1E9PSIsInZhbHVlIjoid3ZUWGJSUTUxZFJnS054T1N1dXgzTUhta05zRFdGWGdLWEhYalVjSTQvUnh3OXpLNklBS3J4UytyV25LUjhIK3ZiQm9PaEdEVWhvR3ZVQlgvYUFxTFZ5Z1VuTit2L3ZhWHFUTE9Cc0YvOVFMWm8vWE1YMUYvbUJPMkJZZG5Dc0szV1JmaVhOVEhwQi92KzhNd3Rab1R1VktEc0EzM1dtdFl6WVAyTlU2cXo0PSIsIm1hYyI6IjI5MzdiMTU1NDhmZDViYjlhZDNiOTE0YmQzOTY0YTA0OGYyNWM3OGJiN2VmMmMzZmE1Zjk3M2Q5ZGJhNmNmOTUiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IkpnNlQ5SzZkak5WSysyUkYxa2d4Smc9PSIsInZhbHVlIjoiRWxvTy94Ym5CTlBvc0NaUGJudGpFNVNHY0NQdUdxNnJqakpibWUzVlI3VmdhVms4VWsySHRrTHJ2QTkvR0Fka28zVGw0aFg5WkVrYVJzbmlEQ2ZSVkNMSmdjNHAvTGlMUFUyWUViUllpVS9mVVlySUtPSUpKZHZQd2dHcnJvdGkiLCJtYWMiOiI5MGZmNWNjZTkxYzBlYzYxZDQwZDhkNmYzOGM1MDIwYzUyZThlMTdmYzlhNDg0YTkyZDZiMGUzYmU1MWUxMWRlIiwidGFnIjoiIn0%3D; zapinflow_session=eyJpdiI6Iit4cmdNY3pULzRoNXhoUUJPcVRhaEE9PSIsInZhbHVlIjoidlJXd2RUeVl0YnlBcDhwOTJ6MTRGTlpvWlNENlUyeDRUTXZEMmFEeU84b2RhVVpVWkNsWkdlWDRRVXFldFh1VlFNSHZrRGhLQVdGL3RWWjBHRjVGb1NJT2xKd28rUVlMVHo1NGRjSVRPemZPQzk4RG1TenNuRzZqbkpVbnpHVGEiLCJtYWMiOiJiZGZjZDgyNzk1ZDI4ZjFiNzBkODE0ZGQxNzc4MmE1NzQ2ZDZiNmIzZjNlM2U3YjVkZThkZTQyMDI5YzE2ZDNkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"41 characters\">en-US,en;q=0.9,id;q=0.8,ms;q=0.7,th;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">http://omnibis.test/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">close</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">omnibis.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1597019252\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-941928423 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_3dc7a913ef5fd4b890ecabe3487085573e16cf82</span>\" => \"<span class=sf-dump-str title=\"400 characters\">eyJpdiI6Ikx2aU14clptQW4zank2S1JqQ1FhV1E9PSIsInZhbHVlIjoid3ZUWGJSUTUxZFJnS054T1N1dXgzTUhta05zRFdGWGdLWEhYalVjSTQvUnh3OXpLNklBS3J4UytyV25LUjhIK3ZiQm9PaEdEVWhvR3ZVQlgvYUFxTFZ5Z1VuTit2L3ZhWHFUTE9Cc0YvOVFMWm8vWE1YMUYvbUJPMkJZZG5Dc0szV1JmaVhOVEhwQi92KzhNd3Rab1R1VktEc0EzM1dtdFl6WVAyTlU2cXo0PSIsIm1hYyI6IjI5MzdiMTU1NDhmZDViYjlhZDNiOTE0YmQzOTY0YTA0OGYyNWM3OGJiN2VmMmMzZmE1Zjk3M2Q5ZGJhNmNmOTUiLCJ0YWciOiIifQ==</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkpnNlQ5SzZkak5WSysyUkYxa2d4Smc9PSIsInZhbHVlIjoiRWxvTy94Ym5CTlBvc0NaUGJudGpFNVNHY0NQdUdxNnJqakpibWUzVlI3VmdhVms4VWsySHRrTHJ2QTkvR0Fka28zVGw0aFg5WkVrYVJzbmlEQ2ZSVkNMSmdjNHAvTGlMUFUyWUViUllpVS9mVVlySUtPSUpKZHZQd2dHcnJvdGkiLCJtYWMiOiI5MGZmNWNjZTkxYzBlYzYxZDQwZDhkNmYzOGM1MDIwYzUyZThlMTdmYzlhNDg0YTkyZDZiMGUzYmU1MWUxMWRlIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>zapinflow_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Iit4cmdNY3pULzRoNXhoUUJPcVRhaEE9PSIsInZhbHVlIjoidlJXd2RUeVl0YnlBcDhwOTJ6MTRGTlpvWlNENlUyeDRUTXZEMmFEeU84b2RhVVpVWkNsWkdlWDRRVXFldFh1VlFNSHZrRGhLQVdGL3RWWjBHRjVGb1NJT2xKd28rUVlMVHo1NGRjSVRPemZPQzk4RG1TenNuRzZqbkpVbnpHVGEiLCJtYWMiOiJiZGZjZDgyNzk1ZDI4ZjFiNzBkODE0ZGQxNzc4MmE1NzQ2ZDZiNmIzZjNlM2U3YjVkZThkZTQyMDI5YzE2ZDNkIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-941928423\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1821143161 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">application/javascript; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 29 May 2026 22:40:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">max-age=31536000, public</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>last-modified</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Tue, 15 Oct 2024 19:35:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 29 May 2025 22:40:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">340160</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-ranges</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">bytes</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1821143161\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-930735062 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-930735062\", {\"maxDepth\":0})</script>\n"}}