<?php

namespace App\Filament\Resources\MidtransSettingResource\Pages;

use App\Filament\Resources\MidtransSettingResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateMidtransSetting extends CreateRecord
{
    protected static string $resource = MidtransSettingResource::class;
    protected static bool $canCreateAnother = false;

    //customize redirect after create
    public function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }
}
