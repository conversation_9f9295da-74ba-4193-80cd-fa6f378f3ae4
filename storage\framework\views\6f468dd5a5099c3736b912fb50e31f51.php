<?php if (isset($component)) { $__componentOriginal166a02a7c5ef5a9331faf66fa665c256 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'filament-panels::components.page.index','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('filament-panels::page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
    <script type="text/javascript" src="https://app.sandbox.midtrans.com/snap/snap.js"
    data-client-key="<?php echo e(env('MIDTRANS_CLIENT_KEY')); ?>"></script>


    <form id="payment-form" class="space-y-4 text-black dark:text-white">
        <div class="">
            <label for="customer_name" class="block text-sm font-medium ">Customer Name</label>
            <input type="text" id="customer_name" name="customer_name" required class="mt-1 dark:bg-black block w-full rounded-md border-gray-300 shadow-sm focus:border-slate-300 focus:ring focus:ring-slate-200 focus:ring-opacity-50">
        </div>
        <div class="">
            <label for="customer_email" class="block text-sm font-medium ">Customer Email</label>
            <input type="email" id="customer_email" name="customer_email" required class="mt-1 dark:bg-black block w-full rounded-md border-gray-300 shadow-sm focus:border-slate-300 focus:ring focus:ring-slate-200 focus:ring-opacity-50">
        </div>
        <div class="">
            <label for="customer_phone" class="block text-sm font-medium ">Customer Phone</label>
            <input type="tel" id="customer_phone" name="customer_phone" required class="mt-1 dark:bg-black block w-full rounded-md border-gray-300 shadow-sm focus:border-slate-300 focus:ring focus:ring-slate-200 focus:ring-opacity-50">
        </div>
        <div class="">
            <label for="amount" class="block text-sm font-medium ">Amount (Rp)</label>
            <input type="number" id="amount" name="amount" required class="mt-1 dark:bg-black block w-full rounded-md border-gray-300 shadow-sm focus:border-slate-300 focus:ring focus:ring-slate-200 focus:ring-opacity-50">
        </div>
        <div class="">
            <label for="app_source_uuid" class="block text-sm font-medium ">App Source</label>
            <select id="app_source_uuid" name="app_source_uuid" required class="mt-1 dark:bg-black block w-full rounded-md border-gray-300 shadow-sm focus:border-slate-300 focus:ring focus:ring-slate-200 focus:ring-opacity-50">
                <option value="">Select App Source</option>
                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $appSources; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $source): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <option value="<?php echo e($source->uuid); ?>"><?php echo e($source->name); ?></option>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
            </select>
        </div>
        <button type="submit" class="inline-flex items-center px-4 py-2 shadow-sm text-sm font-medium rounded-md text-primary-600 bg-black hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500">
            Pay Now
        </button>
       
    </form>

    <script>
        document.getElementById('payment-form').addEventListener('submit', function (e) {
            e.preventDefault();

            // Ambil data dari form
            const customer_name = document.getElementById('customer_name').value;
            const customer_email = document.getElementById('customer_email').value;
            const customer_phone = document.getElementById('customer_phone').value;
            const amount = document.getElementById('amount').value;
            const app_source_uuid = document.getElementById('app_source_uuid').value;

            // Validasi dropdown
            if (!app_source_uuid) {
                alert('Please select an app source');
                return;
            }

            // Fetch token dari API internal
            fetch('/api/payment/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                    'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                },
                body: JSON.stringify({
                    customer_name: customer_name,
                    customer_email: customer_email,
                    customer_phone: customer_phone,
                    amount: amount,
                    app_source_uuid: app_source_uuid
                })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.snap_token) {
                        // Buka Snap payment
                        snap.pay(data.snap_token, {
                            onSuccess: function (result) {
                                alert('Payment success!');
                                console.log(result);
                            },
                            onPending: function (result) {
                                alert('Waiting for payment');
                                console.log(result);
                            },
                            onError: function (result) {
                                alert('Payment failed!');
                                console.log(result);
                            },
                            onClose: function () {
                                alert('Payment popup closed');
                            }
                        });
                    } else {
                        alert('Failed to get payment token: ' + (data.message || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Something went wrong');
                });
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $attributes = $__attributesOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__attributesOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256)): ?>
<?php $component = $__componentOriginal166a02a7c5ef5a9331faf66fa665c256; ?>
<?php unset($__componentOriginal166a02a7c5ef5a9331faf66fa665c256); ?>
<?php endif; ?>

<?php /**PATH D:\laragon\www\omnibis\resources\views/filament/pages/payment-page.blade.php ENDPATH**/ ?>