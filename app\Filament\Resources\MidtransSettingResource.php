<?php

namespace App\Filament\Resources;

use App\Filament\Resources\MidtransSettingResource\Pages;
use App\Filament\Resources\MidtransSettingResource\RelationManagers;
use App\Models\MidtransSetting;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Columns\ToggleColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;

class MidtransSettingResource extends Resource
{
    protected static ?string $model = MidtransSetting::class;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';

    protected static ?string $navigationGroup = 'Payment Settings';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Select::make('account_id')
                    ->label('Account')
                    ->relationship('account', 'name')
                    ->required()
                    ->searchable()
                    ->preload(),

                TextInput::make('merchant_id')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('Your Midtrans Merchant ID'),

                TextInput::make('server_key')
                    ->required()
                    ->password()
                    ->maxLength(255)
                    ->placeholder('Your Midtrans Server Key')
                    ->helperText('This will be encrypted and hidden'),

                TextInput::make('client_key')
                    ->required()
                    ->maxLength(255)
                    ->placeholder('Your Midtrans Client Key'),

                Select::make('environment')
                    ->required()
                    ->options([
                        'sandbox' => 'Sandbox (Testing)',
                        'production' => 'Production (Live)',
                    ])
                    ->default('sandbox')
                    ->helperText('Use sandbox for testing, production for live transactions'),

                Toggle::make('is_active')
                    ->label('Active')
                    ->default(true)
                    ->helperText('Enable or disable this Midtrans configuration'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('account.name')
                    ->label('Account')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('merchant_id')
                    ->searchable()
                    ->sortable(),

                TextColumn::make('masked_server_key')
                    ->label('Server Key')
                    ->getStateUsing(function ($record) {
                        return $record->masked_server_key;
                    }),

                TextColumn::make('masked_client_key')
                    ->label('Client Key')
                    ->getStateUsing(function ($record) {
                        return $record->masked_client_key;
                    }),

                TextColumn::make('environment')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'production' => 'success',
                        'sandbox' => 'warning',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'production' => 'Production',
                        'sandbox' => 'Sandbox',
                        default => ucfirst($state),
                    }),

                ToggleColumn::make('is_active')
                    ->label('Active'),

                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                SelectFilter::make('account')
                    ->relationship('account', 'name')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('environment')
                    ->options([
                        'sandbox' => 'Sandbox',
                        'production' => 'Production',
                    ]),

                TernaryFilter::make('is_active')
                    ->label('Status')
                    ->placeholder('All settings')
                    ->trueLabel('Active only')
                    ->falseLabel('Inactive only'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListMidtransSettings::route('/'),
            'create' => Pages\CreateMidtransSetting::route('/create'),
            'edit' => Pages\EditMidtransSetting::route('/{record}/edit'),
        ];
    }
}
