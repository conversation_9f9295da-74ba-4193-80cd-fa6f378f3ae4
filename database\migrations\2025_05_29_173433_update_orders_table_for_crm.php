<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Drop existing columns that don't match our PRD
            $table->dropForeign(['app_source_uuid']);
            $table->dropColumn([
                'order_id',
                'customer_name',
                'customer_email',
                'customer_phone',
                'amount',
                'payment_status',
                'app_source_uuid'
            ]);

            // Add new columns according to PRD
            $table->foreignId('account_id')->constrained()->onDelete('cascade');
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->decimal('price', 15, 2);
            $table->enum('status', ['pending', 'shipped', 'delivered'])->default('pending');
            $table->integer('quantity_ordered')->default(1);
            $table->json('custom_fields')->nullable();

            // Add indexes
            $table->index(['account_id', 'status']);
            $table->index('customer_id');
            $table->index('product_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            // Drop new columns
            $table->dropForeign(['account_id']);
            $table->dropForeign(['customer_id']);
            $table->dropForeign(['product_id']);
            $table->dropColumn([
                'account_id',
                'customer_id',
                'product_id',
                'price',
                'status',
                'quantity_ordered',
                'custom_fields'
            ]);

            // Restore original columns
            $table->string('order_id')->unique();
            $table->string('customer_name');
            $table->string('customer_email');
            $table->string('customer_phone');
            $table->decimal('amount', 10, 2);
            $table->string('payment_status')->default('pending');
            $table->uuid('app_source_uuid');
            $table->foreign('app_source_uuid')->references('uuid')->on('app_sources')->onDelete('restrict');
        });
    }
};
