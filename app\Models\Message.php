<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Message extends Model
{
    use HasFactory;

    protected $fillable = [
        'account_id',
        'sender',
        'message',
        'timestamp',
        'channel',
        'direction',
        'message_id',
        'metadata',
    ];

    protected $casts = [
        'timestamp' => 'datetime',
        'metadata' => 'array',
    ];

    // Relationships
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    // Scopes
    public function scopeWhatsApp($query)
    {
        return $query->where('channel', 'whatsapp');
    }

    public function scopeTelegram($query)
    {
        return $query->where('channel', 'telegram');
    }

    public function scopeIncoming($query)
    {
        return $query->where('direction', 'incoming');
    }

    public function scopeOutgoing($query)
    {
        return $query->where('direction', 'outgoing');
    }

    public function scopeRecent($query)
    {
        return $query->orderBy('timestamp', 'desc');
    }

    // Accessors
    public function getChannelDisplayAttribute(): string
    {
        return match($this->channel) {
            'whatsapp' => 'WhatsApp',
            'telegram' => 'Telegram',
            default => ucfirst($this->channel)
        };
    }

    public function getDirectionDisplayAttribute(): string
    {
        return match($this->direction) {
            'incoming' => 'Incoming',
            'outgoing' => 'Outgoing',
            default => ucfirst($this->direction)
        };
    }

    public function getShortMessageAttribute(): string
    {
        return strlen($this->message) > 50 
            ? substr($this->message, 0, 50) . '...' 
            : $this->message;
    }
}
